<template>
  <div class="common-layout">
    <el-container class="app-container">
      <el-header class="app-header">
        <img src="../assets/logo.svg" id="logo" />
        <div id="user" @mouseenter="showDropdown" @mouseleave="hideDropdown">
          <img src="../assets/preview.jpg" id="userimg" />
          <span @click="updateNickname">{{ nickname }}</span>
          <div id="dropdown" v-if="isDropdownVisible">
            <!-- 下拉框内容 -->
            <ul>
              <li><span @click="toPersonalCenter">个人中心</span></li>
              <li><span @click="toUpdatePwd
                ">修改密码</span></li>
              <li><span @click="logout">退出登录</span></li>
            </ul>
          </div>
        </div>
      </el-header>
      <el-container class="main-container">
        <el-aside width="220px" class="app-aside">
          <el-row class="tac" id="aside-tac">
            <el-col :span="24">
              <el-menu default-active="dashboard" class="el-menu-vertical-demo" @open="handleOpen" @close="handleClose">
                <!-- 仪表盘 -->
                <el-menu-item index="dashboard" @click="toDashboard">
                  <el-icon><Odometer /></el-icon>
                  <span>仪表盘</span>
                </el-menu-item>

                <!-- 收支管理 -->
                <el-sub-menu index="income-expense">
                  <template #title>
                    <el-icon><Money /></el-icon>
                    <span>收支管理</span>
                  </template>
                  <el-menu-item index="income-expense-1" @click="toIncomeRecord">快速记账</el-menu-item>
                  <el-menu-item index="income-expense-2" @click="toExpenseRecord">收支记录</el-menu-item>
                  <el-menu-item index="income-expense-3" @click="toCategory">分类管理</el-menu-item>
                </el-sub-menu>

                <!-- 家庭成员 -->
                <el-menu-item index="family-members" @click="toFamilyMembers">
                  <el-icon><User /></el-icon>
                  <span>家庭成员</span>
                </el-menu-item>

                <!-- 资金账户 -->
                <el-menu-item index="accounts" @click="toAccounts">
                  <el-icon><CreditCard /></el-icon>
                  <span>资金账户</span>
                </el-menu-item>

                <!-- 预算管理 -->
                <el-menu-item index="budget" @click="toBudget">
                  <el-icon><TrendCharts /></el-icon>
                  <span>预算管理</span>
                </el-menu-item>

                <!-- 统计报表 -->
                <el-sub-menu index="reports">
                  <template #title>
                    <el-icon><DataAnalysis /></el-icon>
                    <span>统计报表</span>
                  </template>
                  <el-menu-item index="reports-1" @click="toReports">收支统计</el-menu-item>
                  <el-menu-item index="reports-2" @click="toTrends">趋势分析</el-menu-item>
                  <el-menu-item index="reports-3" @click="toMemberStats">成员对比</el-menu-item>
                </el-sub-menu>

                <!-- 系统设置 -->
                <el-menu-item index="settings" @click="tosetting">
                  <el-icon><Setting /></el-icon>
                  <span>系统设置</span>
                </el-menu-item>
              </el-menu>
            </el-col>
          </el-row>
        </el-aside>
        <el-container class="content-container">
          <el-main class="app-main">
            <router-view></router-view>
          </el-main>
          <el-footer class="app-footer">
            <h5>@YJH 2000-3000</h5>
          </el-footer>
        </el-container>
      </el-container>
    </el-container>
  </div>
</template>

<script>
export default {
  name: 'HomeIndex',
  data() {
    return {
      // nickname: sessionStorage.getItem('nickname') || 'testuser',
      nickname: localStorage.getItem('nickname'),
      isDropdownVisible: false, // 控制下拉框显示状态
    };
  },
  methods: {
    // 原有方法
    tosetting() {
      this.$router.push('/admin/MyPage');
    },
    toshow() {
      this.$router.push('/admin/ShowMe');
    },
    toPersonalCenter() {
      this.$router.push('/admin/PersonalCenter');
    },
    toUpdatePwd() {
      this.$router.push('/admin/UpdatePwd');
    },

    // 新增的财务管理导航方法
    toDashboard() {
      this.$router.push('/admin/Dashboard');
    },
    toIncomeRecord() {
      this.$router.push('/admin/IncomeRecord');
    },
    toExpenseRecord() {
      this.$router.push('/admin/ExpenseRecord');
    },
    toCategory() {
      this.$router.push('/admin/Category');
    },
    toFamilyMembers() {
      this.$router.push('/admin/FamilyMembers');
    },
    toAccounts() {
      this.$router.push('/admin/Accounts');
    },
    toBudget() {
      this.$router.push('/admin/Budget');
    },
    toReports() {
      this.$router.push('/admin/Reports');
    },
    toTrends() {
      this.$router.push('/admin/Trends');
    },
    toMemberStats() {
      this.$router.push('/admin/MemberStats');
    },
    showDropdown() {
      this.isDropdownVisible = true;
    },
    hideDropdown() {
      this.isDropdownVisible = false;
    },
    logout() {
      // 弹出提示框，询问用户是否确认退出
      this.$confirm('确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        // 确认退出
        // 清空昵称
        // sessionStorage.removeItem('nickname');
        localStorage.removeItem('nickname');
        // 跳转到登录页
        this.$router.push('/');
      }).catch(() => {
        // 取消退出
      });
    },
  },
  mounted() {
    this.toDashboard();
  },
};
</script>


<style>
#logo {
  text-align: left;
}

#user {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 150px;
  width: 150px;
  overflow: visible !important;
  cursor: pointer;
}

#userimg {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  margin-right: 5px;
  flex-shrink: 0;
}

#user span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 115px;
}

#dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: rgb(255, 255, 255, 0.5);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  overflow: hidden;
  z-index: 9999;
  min-width: 150px;
}

#dropdown ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

#dropdown ul li {
  padding: 10px;
  text-align: center; /* 居中内容 */
  height: auto; /* 自动适应内容高度 */
  line-height: 20px; /* 垂直居中对齐 */
}

#dropdown ul li span {
  display: inline-block;
  width: 100%; /* 占据父容器宽度 */
  text-align: center; /* 居中对齐文字 */
  font-size: 14px; /* 调整字体大小 */
  color: #333;
}

#dropdown ul li:hover {
  background-color: rgb(255, 255, 255, 0.3);
}

/* 整个页面布局 */
.common-layout {
  width: 100vw;
  /* 视口宽度 */
  min-height: 100vh;
  /* 最小视口高度 */
  overflow: auto;
  /* 允许滚动 */
}

/* 最外层容器 */
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.app-header {
  background-color: rgb(255, 255, 255, 0.7);
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  line-height: 60px;
  flex-shrink: 0;
  padding: 0 20px;
  /* 可选：左右内边距 */
  overflow: visible !important; /* 确保子元素不被裁剪 */
}



#headimg {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

/* 主要内容区 */
.main-container {
  flex: 1;
  /* 占据剩余所有空间 */
  display: flex;
}

/* 侧边栏 */
.app-aside {
  background-color: rgb(255, 255, 255, 0.3);
  color: #333;
  text-align: left;
  /* 调整Aside高度 */
  flex-shrink: 0;
  /* 确保aside不被压缩 */
  overflow-y: auto;
}

.app-aside .el-row,
.app-aside .el-col,
.app-aside .el-menu,
.app-aside .el-menu-item {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 内容区容器 */
.content-container {
  flex: 1;
  /* 占据剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 允许flex子项收缩 */
}

/* 主要内容 */
.app-main {
  padding: 5px;
  background-color: rgb(255, 255, 255, 0.2);
  color: #333;
  flex: 1;
  /* 占据内容容器剩余空间 */
  overflow: visible;
  /* 允许内容自然流动 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子项可以收缩 */
}


/* 底部 */
.app-footer {
  background-color: rgb(255, 255, 255, 0.5);
  color: #333;
  text-align: left;
  line-height: 20px;
  /* 调整Footer高度 */
  flex-shrink: 0;
  /* 确保footer不被压缩 */
}
</style>