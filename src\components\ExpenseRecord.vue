<template>
  <div class="expense-record">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><Document /></el-icon>
        收支记录
      </h2>
      <p class="page-subtitle">查看和管理所有收支记录</p>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <div class="filter-form-wrapper">
          <el-form :model="filterForm" inline class="filter-form">
            <el-form-item label="类型">
              <el-select v-model="filterForm.type" placeholder="全部类型" clearable style="width: 120px">
                <el-option label="收入" value="income" />
                <el-option label="支出" value="expense" />
              </el-select>
            </el-form-item>

            <el-form-item label="分类">
              <el-select v-model="filterForm.category" placeholder="全部分类" clearable style="width: 150px">
                <el-option
                  v-for="category in allCategories"
                  :key="category"
                  :label="category"
                  :value="category"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="成员">
              <el-select v-model="filterForm.member" placeholder="全部成员" clearable style="width: 120px">
                <el-option
                  v-for="member in familyMembers"
                  :key="member.value"
                  :label="member.label"
                  :value="member.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="账户">
              <el-select v-model="filterForm.account" placeholder="全部账户" clearable style="width: 150px">
                <el-option
                  v-for="account in accounts"
                  :key="account.value"
                  :label="account.label"
                  :value="account.value"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="日期">
              <el-date-picker
                v-model="filterForm.dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 240px"
              />
            </el-form-item>
          </el-form>

          <div class="filter-actions">
            <el-button type="primary" @click="applyFilter" icon="Search">搜索</el-button>
            <el-button @click="resetFilter" icon="Refresh">重置</el-button>
            <el-button type="success" @click="toIncomeRecord" icon="Plus">新增记录</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计概览 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-item income">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <p class="stat-label">总收入</p>
            <p class="stat-value">¥{{ filteredStats.totalIncome.toLocaleString() }}</p>
          </div>
        </div>
        
        <div class="stat-item expense">
          <div class="stat-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <p class="stat-label">总支出</p>
            <p class="stat-value">¥{{ filteredStats.totalExpense.toLocaleString() }}</p>
          </div>
        </div>
        
        <div class="stat-item balance">
          <div class="stat-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="stat-content">
            <p class="stat-label">净收入</p>
            <p class="stat-value" :class="filteredStats.netIncome >= 0 ? 'positive' : 'negative'">
              ¥{{ filteredStats.netIncome.toLocaleString() }}
            </p>
          </div>
        </div>
        
        <div class="stat-item count">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <p class="stat-label">记录数量</p>
            <p class="stat-value">{{ filteredRecords.length }}条</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 记录列表 -->
    <div class="records-section">
      <el-card class="records-card">
        <template #header>
          <div class="card-header">
            <span>收支记录</span>
            <div class="header-actions">
              <el-button type="text" @click="exportRecords" icon="Download">导出</el-button>
              <el-button type="text" @click="refreshRecords" icon="Refresh">刷新</el-button>
            </div>
          </div>
        </template>

        <div class="table-container">
          <el-table
            :data="paginatedRecords"
            style="width: 100%"
            :loading="loading"
            @selection-change="handleSelectionChange"
          >
          <el-table-column type="selection" width="55" />
          
          <el-table-column prop="date" label="日期" width="120" sortable>
            <template #default="scope">
              {{ formatDate(scope.row.date) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'income' ? 'success' : 'danger'" size="small">
                {{ scope.row.type === 'income' ? '收入' : '支出' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="分类" width="100" />
          
          <el-table-column prop="amount" label="金额" width="120" sortable>
            <template #default="scope">
              <span :class="scope.row.type === 'income' ? 'amount-income' : 'amount-expense'">
                {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ scope.row.amount.toLocaleString() }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="account" label="账户" width="120" />
          <el-table-column prop="member" label="成员" width="100" />
          <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
          
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button type="text" size="small" @click="editRecord(scope.row)" icon="Edit">编辑</el-button>
              <el-button type="text" size="small" @click="deleteRecord(scope.row)" icon="Delete" class="delete-btn">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="filteredRecords.length"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedRecords.length > 0" class="batch-actions">
      <el-card>
        <div class="batch-content">
          <span>已选择 {{ selectedRecords.length }} 条记录</span>
          <div class="batch-buttons">
            <el-button type="danger" @click="batchDelete" icon="Delete">批量删除</el-button>
            <el-button @click="clearSelection" icon="Close">取消选择</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'

export default {
  name: 'ExpenseRecord',
  setup() {
    const loading = ref(false)
    const currentPage = ref(1)
    const pageSize = ref(20)
    const selectedRecords = ref([])

    // 筛选表单
    const filterForm = reactive({
      type: '',
      category: '',
      member: '',
      account: '',
      dateRange: []
    })

    // 家庭成员
    const familyMembers = reactive([
      { label: '张三', value: '张三' },
      { label: '李四', value: '李四' },
      { label: '张小明', value: '张小明' },
      { label: '张奶奶', value: '张奶奶' }
    ])

    // 账户列表
    const accounts = reactive([
      { label: '工商银行', value: '工商银行' },
      { label: '建设银行', value: '建设银行' },
      { label: '支付宝', value: '支付宝' },
      { label: '微信', value: '微信' },
      { label: '现金', value: '现金' }
    ])

    // 所有分类
    const allCategories = [
      '工资', '奖金', '兼职', '投资收益', '红包', '其他收入',
      '餐饮', '交通', '购物', '娱乐', '医疗', '教育', '住房', '通讯', '日用品', '其他支出'
    ]

    // 模拟记录数据
    const allRecords = reactive([
      {
        id: 1,
        date: '2024-06-24 09:00:00',
        type: 'income',
        category: '工资',
        amount: 8500,
        account: '工商银行',
        member: '张三',
        remark: '6月工资'
      },
      {
        id: 2,
        date: '2024-06-23 12:30:00',
        type: 'expense',
        category: '餐饮',
        amount: 268,
        account: '支付宝',
        member: '张三',
        remark: '午餐'
      },
      {
        id: 3,
        date: '2024-06-22 18:45:00',
        type: 'expense',
        category: '交通',
        amount: 156,
        account: '微信',
        member: '李四',
        remark: '打车费'
      },
      {
        id: 4,
        date: '2024-06-21 15:20:00',
        type: 'expense',
        category: '购物',
        amount: 1200,
        account: '信用卡',
        member: '李四',
        remark: '买衣服'
      },
      {
        id: 5,
        date: '2024-06-20 10:30:00',
        type: 'income',
        category: '兼职',
        amount: 800,
        account: '支付宝',
        member: '张小明',
        remark: '兼职收入'
      },
      {
        id: 6,
        date: '2024-06-19 14:20:00',
        type: 'expense',
        category: '医疗',
        amount: 450,
        account: '工商银行',
        member: '张奶奶',
        remark: '体检费用'
      },
      {
        id: 7,
        date: '2024-06-18 16:15:00',
        type: 'expense',
        category: '教育',
        amount: 2800,
        account: '建设银行',
        member: '张小明',
        remark: '培训费'
      },
      {
        id: 8,
        date: '2024-06-17 11:30:00',
        type: 'income',
        category: '投资收益',
        amount: 1200,
        account: '支付宝',
        member: '张三',
        remark: '基金收益'
      },
      {
        id: 9,
        date: '2024-06-16 19:45:00',
        type: 'expense',
        category: '娱乐',
        amount: 380,
        account: '微信',
        member: '李四',
        remark: '看电影'
      },
      {
        id: 10,
        date: '2024-06-15 08:20:00',
        type: 'expense',
        category: '住房',
        amount: 3500,
        account: '工商银行',
        member: '张三',
        remark: '房租'
      },
      {
        id: 11,
        date: '2024-06-14 13:10:00',
        type: 'expense',
        category: '通讯',
        amount: 89,
        account: '支付宝',
        member: '张三',
        remark: '手机话费'
      },
      {
        id: 12,
        date: '2024-06-13 17:30:00',
        type: 'expense',
        category: '日用品',
        amount: 156,
        account: '微信',
        member: '李四',
        remark: '洗护用品'
      },
      {
        id: 13,
        date: '2024-06-12 09:45:00',
        type: 'income',
        category: '红包',
        amount: 200,
        account: '微信',
        member: '张小明',
        remark: '生日红包'
      },
      {
        id: 14,
        date: '2024-06-11 15:20:00',
        type: 'expense',
        category: '餐饮',
        amount: 128,
        account: '支付宝',
        member: '张奶奶',
        remark: '晚餐'
      },
      {
        id: 15,
        date: '2024-06-10 12:00:00',
        type: 'expense',
        category: '交通',
        amount: 45,
        account: '现金',
        member: '张三',
        remark: '地铁费'
      }
    ])

    // 筛选后的记录
    const filteredRecords = computed(() => {
      let records = [...allRecords]
      
      if (filterForm.type) {
        records = records.filter(record => record.type === filterForm.type)
      }
      
      if (filterForm.category) {
        records = records.filter(record => record.category === filterForm.category)
      }
      
      if (filterForm.member) {
        records = records.filter(record => record.member === filterForm.member)
      }
      
      if (filterForm.account) {
        records = records.filter(record => record.account === filterForm.account)
      }
      
      if (filterForm.dateRange && filterForm.dateRange.length === 2) {
        const [startDate, endDate] = filterForm.dateRange
        records = records.filter(record => {
          const recordDate = dayjs(record.date).format('YYYY-MM-DD')
          return recordDate >= startDate && recordDate <= endDate
        })
      }
      
      return records.sort((a, b) => new Date(b.date) - new Date(a.date))
    })

    // 分页后的记录
    const paginatedRecords = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value
      const end = start + pageSize.value
      return filteredRecords.value.slice(start, end)
    })

    // 筛选统计
    const filteredStats = computed(() => {
      const totalIncome = filteredRecords.value
        .filter(record => record.type === 'income')
        .reduce((sum, record) => sum + record.amount, 0)
      
      const totalExpense = filteredRecords.value
        .filter(record => record.type === 'expense')
        .reduce((sum, record) => sum + record.amount, 0)
      
      return {
        totalIncome,
        totalExpense,
        netIncome: totalIncome - totalExpense
      }
    })

    return {
      loading,
      currentPage,
      pageSize,
      selectedRecords,
      filterForm,
      familyMembers,
      accounts,
      allCategories,
      allRecords,
      filteredRecords,
      paginatedRecords,
      filteredStats
    }
  },
  methods: {
    // 应用筛选
    applyFilter() {
      this.currentPage = 1
    },

    // 重置筛选
    resetFilter() {
      Object.assign(this.filterForm, {
        type: '',
        category: '',
        member: '',
        account: '',
        dateRange: []
      })
      this.currentPage = 1
    },

    // 跳转到记账页面
    toIncomeRecord() {
      this.$router.push('/admin/IncomeRecord')
    },

    // 格式化日期
    formatDate(dateString) {
      return dayjs(dateString).format('MM-DD HH:mm')
    },

    // 编辑记录
    editRecord() {
      this.$message.info('编辑功能开发中...')
    },

    // 删除记录
    deleteRecord(record) {
      this.$confirm('确定要删除这条记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.allRecords.findIndex(r => r.id === record.id)
        if (index > -1) {
          this.allRecords.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRecords = selection
    },

    // 批量删除
    batchDelete() {
      this.$confirm(`确定要删除选中的 ${this.selectedRecords.length} 条记录吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const selectedIds = this.selectedRecords.map(record => record.id)
        for (let i = this.allRecords.length - 1; i >= 0; i--) {
          if (selectedIds.includes(this.allRecords[i].id)) {
            this.allRecords.splice(i, 1)
          }
        }
        this.selectedRecords = []
        this.$message.success('批量删除成功')
      }).catch(() => {})
    },

    // 清除选择
    clearSelection() {
      this.selectedRecords = []
    },

    // 导出记录
    exportRecords() {
      this.$message.info('导出功能开发中...')
    },

    // 刷新记录
    refreshRecords() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('刷新成功')
      }, 1000)
    },

    // 分页大小变化
    handleSizeChange(newSize) {
      this.pageSize = newSize
      this.currentPage = 1
    },

    // 当前页变化
    handleCurrentChange(newPage) {
      this.currentPage = newPage
    }
  }
}
</script>

<style scoped>
.expense-record {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(100vh - 120px);
  min-height: 500px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 10px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 筛选区域 */
.filter-section {
  flex-shrink: 0;
}

.filter-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* 筛选表单包装器 */
.filter-form-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-form {
  margin: 0;
  flex: 1;
  min-width: 0;
}

.filter-form .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.filter-form .el-form-item:last-child {
  margin-right: 0;
}

/* 筛选操作按钮 */
.filter-actions {
  display: flex;
  gap: 10px;
  flex-shrink: 0;
  align-items: flex-end;
}

/* 统计区域 */
.stats-section {
  flex-shrink: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.stat-item {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stat-item.income .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-item.expense .stat-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.stat-item.balance .stat-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.stat-item.count .stat-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.stat-content {
  flex: 1;
}

.stat-label {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #7f8c8d;
}

.stat-value {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.stat-value.positive {
  color: #52c41a;
}

.stat-value.negative {
  color: #ff4d4f;
}

/* 记录区域 */
.records-section {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.records-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 表格容器 */
.table-container {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  max-height: 100%;
  padding-right: 5px;
}

/* 表格容器滚动条样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 表格样式 */
.amount-income {
  color: #52c41a;
  font-weight: 600;
}

.amount-expense {
  color: #ff4d4f;
  font-weight: 600;
}

.delete-btn {
  color: #ff4d4f !important;
}

.delete-btn:hover {
  color: #ff7875 !important;
}

/* 分页 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

/* 批量操作 */
.batch-actions {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.batch-actions .el-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.batch-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 10px 20px;
}

.batch-buttons {
  display: flex;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .filter-form-wrapper {
    flex-direction: column;
    gap: 15px;
  }

  .filter-form {
    flex-wrap: wrap;
  }

  .filter-form .el-form-item {
    margin-bottom: 15px;
  }

  .filter-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .expense-record {
    padding: 15px;
    gap: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .stat-item {
    padding: 15px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 18px;
  }

  .filter-form-wrapper {
    flex-direction: column;
    gap: 15px;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    width: 100%;
  }

  .filter-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .batch-actions {
    left: 10px;
    right: 10px;
    transform: none;
  }

  .batch-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .expense-record {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .stat-item {
    padding: 12px;
    gap: 10px;
  }

  .stat-icon {
    width: 35px;
    height: 35px;
    font-size: 16px;
  }

  .stat-value {
    font-size: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 5px;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-card__body) {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table .el-table__body-wrapper) {
  overflow-y: visible;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-table tr:hover > td) {
  background: rgba(255, 255, 255, 0.8);
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

:deep(.el-tag) {
  border-radius: 6px;
}

:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 6px;
}

/* 主容器滚动条样式 */
.expense-record::-webkit-scrollbar {
  width: 6px;
}

.expense-record::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.expense-record::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.expense-record::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
