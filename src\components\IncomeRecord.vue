<template>
  <div class="income-record">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><Money /></el-icon>
        快速记账
      </h2>
      <p class="page-subtitle">记录您的收入和支出</p>
    </div>

    <!-- 记账表单 -->
    <div class="record-form-container">
      <el-card class="record-form-card">
        <template #header>
          <div class="card-header">
            <span>新增记录</span>
            <el-button type="text" @click="resetForm">重置</el-button>
          </div>
        </template>

        <el-form
          :model="recordForm"
          :rules="formRules"
          ref="recordFormRef"
          label-width="100px"
          class="record-form"
        >
          <!-- 收支类型 -->
          <el-form-item label="类型" prop="type">
            <el-radio-group v-model="recordForm.type" @change="onTypeChange">
              <el-radio-button value="income">
                <el-icon><Plus /></el-icon>
                收入
              </el-radio-button>
              <el-radio-button value="expense">
                <el-icon><Minus /></el-icon>
                支出
              </el-radio-button>
            </el-radio-group>
          </el-form-item>

          <!-- 金额 -->
          <el-form-item label="金额" prop="amount">
            <el-input
              v-model="recordForm.amount"
              placeholder="请输入金额"
              type="number"
              step="0.01"
              min="0"
              clearable
            >
              <template #prefix>¥</template>
            </el-input>
          </el-form-item>

          <!-- 分类 -->
          <el-form-item label="分类" prop="category">
            <el-select
              v-model="recordForm.category"
              placeholder="请选择分类"
              filterable
              allow-create
              style="width: 100%"
            >
              <el-option
                v-for="category in currentCategories"
                :key="category.value"
                :label="category.label"
                :value="category.value"
              />
            </el-select>
          </el-form-item>

          <!-- 账户 -->
          <el-form-item label="账户" prop="account">
            <el-select
              v-model="recordForm.account"
              placeholder="请选择账户"
              style="width: 100%"
            >
              <el-option
                v-for="account in accounts"
                :key="account.value"
                :label="account.label"
                :value="account.value"
              >
                <span style="float: left">{{ account.label }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">
                  ¥{{ account.balance.toLocaleString() }}
                </span>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 成员 -->
          <el-form-item label="成员" prop="member">
            <el-select
              v-model="recordForm.member"
              placeholder="请选择成员"
              style="width: 100%"
            >
              <el-option
                v-for="member in familyMembers"
                :key="member.value"
                :label="member.label"
                :value="member.value"
              />
            </el-select>
          </el-form-item>

          <!-- 日期 -->
          <el-form-item label="日期" prop="date">
            <el-date-picker
              v-model="recordForm.date"
              type="datetime"
              placeholder="选择日期时间"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              style="width: 100%"
            />
          </el-form-item>

          <!-- 备注 -->
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="recordForm.remark"
              type="textarea"
              placeholder="请输入备注（可选）"
              :rows="3"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <!-- 提交按钮 -->
          <el-form-item>
            <el-button
              type="primary"
              @click="submitRecord"
              :loading="submitting"
              size="large"
              style="width: 100%"
            >
              <el-icon><Check /></el-icon>
              保存记录
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 最近记录 -->
    <div class="recent-records">
      <h3>最近记录</h3>
      <div class="records-list">
        <div
          v-for="record in recentRecords"
          :key="record.id"
          class="record-item"
          @click="copyRecord(record)"
        >
          <div class="record-icon" :class="record.type">
            <el-icon v-if="record.type === 'income'"><Plus /></el-icon>
            <el-icon v-else><Minus /></el-icon>
          </div>
          <div class="record-info">
            <p class="record-title">{{ record.category }}</p>
            <p class="record-detail">{{ record.account }} · {{ record.member }}</p>
            <p class="record-time">{{ formatDate(record.date) }}</p>
          </div>
          <div class="record-amount" :class="record.type">
            {{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount.toLocaleString() }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'

export default {
  name: 'IncomeRecord',
  setup() {
    const recordFormRef = ref(null)
    const submitting = ref(false)

    // 表单数据
    const recordForm = reactive({
      type: 'expense',
      amount: '',
      category: '',
      account: '',
      member: '',
      date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      remark: ''
    })

    // 表单验证规则
    const formRules = {
      type: [
        { required: true, message: '请选择类型', trigger: 'change' }
      ],
      amount: [
        { required: true, message: '请输入金额', trigger: 'blur' },
        { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
      ],
      category: [
        { required: true, message: '请选择分类', trigger: 'change' }
      ],
      account: [
        { required: true, message: '请选择账户', trigger: 'change' }
      ],
      member: [
        { required: true, message: '请选择成员', trigger: 'change' }
      ],
      date: [
        { required: true, message: '请选择日期', trigger: 'change' }
      ]
    }

    // 收入分类
    const incomeCategories = [
      { label: '工资', value: '工资' },
      { label: '奖金', value: '奖金' },
      { label: '兼职', value: '兼职' },
      { label: '投资收益', value: '投资收益' },
      { label: '红包', value: '红包' },
      { label: '其他收入', value: '其他收入' }
    ]

    // 支出分类
    const expenseCategories = [
      { label: '餐饮', value: '餐饮' },
      { label: '交通', value: '交通' },
      { label: '购物', value: '购物' },
      { label: '娱乐', value: '娱乐' },
      { label: '医疗', value: '医疗' },
      { label: '教育', value: '教育' },
      { label: '住房', value: '住房' },
      { label: '通讯', value: '通讯' },
      { label: '日用品', value: '日用品' },
      { label: '其他支出', value: '其他支出' }
    ]

    // 当前分类（根据类型动态变化）
    const currentCategories = computed(() => {
      return recordForm.type === 'income' ? incomeCategories : expenseCategories
    })

    // 账户列表
    const accounts = reactive([
      { label: '工商银行', value: '工商银行', balance: 25600 },
      { label: '建设银行', value: '建设银行', balance: 18900 },
      { label: '支付宝', value: '支付宝', balance: 3200 },
      { label: '微信', value: '微信', balance: 1800 },
      { label: '现金', value: '现金', balance: 500 }
    ])

    // 家庭成员
    const familyMembers = reactive([
      { label: '张三', value: '张三' },
      { label: '李四', value: '李四' },
      { label: '张小明', value: '张小明' },
      { label: '张奶奶', value: '张奶奶' }
    ])

    // 最近记录
    const recentRecords = reactive([
      {
        id: 1,
        type: 'expense',
        amount: 268,
        category: '餐饮',
        account: '支付宝',
        member: '张三',
        date: '2024-06-23 12:30:00',
        remark: '午餐'
      },
      {
        id: 2,
        type: 'income',
        amount: 8500,
        category: '工资',
        account: '工商银行',
        member: '张三',
        date: '2024-06-22 09:00:00',
        remark: '6月工资'
      },
      {
        id: 3,
        type: 'expense',
        amount: 156,
        category: '交通',
        account: '微信',
        member: '李四',
        date: '2024-06-21 18:45:00',
        remark: '打车费'
      }
    ])

    return {
      recordFormRef,
      submitting,
      recordForm,
      formRules,
      currentCategories,
      accounts,
      familyMembers,
      recentRecords
    }
  },
  methods: {
    // 类型改变时重置分类
    onTypeChange() {
      this.recordForm.category = ''
    },

    // 复制记录
    copyRecord(record) {
      Object.assign(this.recordForm, {
        type: record.type,
        category: record.category,
        account: record.account,
        member: record.member,
        amount: '',
        date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        remark: ''
      })
    },

    // 提交记录
    async submitRecord() {
      try {
        const valid = await this.$refs.recordFormRef.validate()
        if (!valid) return

        this.submitting = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 添加到最近记录
        const newRecord = {
          id: Date.now(),
          ...this.recordForm,
          amount: parseFloat(this.recordForm.amount)
        }
        this.recentRecords.unshift(newRecord)
        if (this.recentRecords.length > 10) {
          this.recentRecords.pop()
        }

        this.$message.success('记录保存成功')
        this.resetForm()
      } catch (error) {
        this.$message.error('保存失败，请重试')
      } finally {
        this.submitting = false
      }
    },

    // 重置表单
    resetForm() {
      Object.assign(this.recordForm, {
        type: 'expense',
        amount: '',
        category: '',
        account: '',
        member: '',
        date: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        remark: ''
      })
      this.$refs.recordFormRef?.clearValidate()
    },

    // 格式化日期
    formatDate(dateString) {
      return dayjs(dateString).format('MM-DD HH:mm')
    }
  }
}
</script>

<style scoped>
.income-record {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 25px;
  height: calc(100vh - 120px);
  min-height: 500px;
}

/* 页面标题 */
.page-header {
  grid-column: 1 / -1;
  margin-bottom: 20px;
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 记账表单容器 */
.record-form-container {
  grid-column: 1;
}

.record-form-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.record-form {
  padding: 10px 0;
}

.record-form .el-form-item {
  margin-bottom: 20px;
}

/* 最近记录 */
.recent-records {
  grid-column: 2;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  height: fit-content;
  max-height: 400px;
  overflow-y: auto;
}

.recent-records h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.records-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.record-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s ease;
}

.record-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.record-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  flex-shrink: 0;
}

.record-icon.income {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.record-icon.expense {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.record-info {
  flex: 1;
  min-width: 0;
}

.record-title {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
}

.record-detail {
  margin: 0 0 2px 0;
  font-size: 11px;
  color: #7f8c8d;
}

.record-time {
  margin: 0;
  font-size: 11px;
  color: #bdc3c7;
}

.record-amount {
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.record-amount.income {
  color: #52c41a;
}

.record-amount.expense {
  color: #ff4d4f;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .income-record {
    grid-template-columns: 1fr 250px;
    gap: 20px;
  }
}

@media (max-width: 1024px) {
  .income-record {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .recent-records {
    grid-column: 1;
  }
}

@media (max-width: 768px) {
  .income-record {
    padding: 15px;
    gap: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .record-form-card,
  .recent-records {
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .income-record {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .record-form-card,
  .recent-records {
    padding: 12px;
  }

  .record-item {
    padding: 10px;
    gap: 10px;
  }

  .record-icon {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-card__header) {
  padding: 18px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-radius: 8px 0 0 8px;
}

:deep(.el-radio-button:last-child .el-radio-button__inner) {
  border-radius: 0 8px 8px 0;
}

:deep(.el-date-editor.el-input) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

/* 滚动条样式 */
.income-record::-webkit-scrollbar,
.recent-records::-webkit-scrollbar {
  width: 6px;
}

.income-record::-webkit-scrollbar-track,
.recent-records::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.income-record::-webkit-scrollbar-thumb,
.recent-records::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.income-record::-webkit-scrollbar-thumb:hover,
.recent-records::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
